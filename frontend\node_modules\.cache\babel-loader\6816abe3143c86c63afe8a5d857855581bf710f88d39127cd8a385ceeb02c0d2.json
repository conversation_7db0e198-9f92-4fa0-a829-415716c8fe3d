{"ast": null, "code": "var _jsxFileName = \"C:\\\\Spaces\\\\WebsiteRosetta\\\\project-044\\\\VastWizardv2.0\\\\frontend\\\\src\\\\App.tsx\";\nimport React from 'react';\nimport { ThemeProvider } from '@mui/material/styles';\nimport { CssBaseline } from '@mui/material';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport theme from './theme';\nimport TemplateWizard from './pages/TemplateWizard';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n    theme: theme,\n    children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(TemplateWizard, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 15,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "ThemeProvider", "CssBaseline", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "theme", "Template<PERSON><PERSON>rd", "jsxDEV", "_jsxDEV", "App", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "_c", "$RefreshReg$"], "sources": ["C:/Spaces/WebsiteRosetta/project-044/VastWizardv2.0/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { ThemeProvider } from '@mui/material/styles';\nimport { CssBaseline } from '@mui/material';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport theme from './theme';\nimport TemplateWizard from './pages/TemplateWizard';\nimport { Box, Container, Typography } from '@mui/material';\n\nfunction App() {\n  return (\n    <ThemeProvider theme={theme}>\n      <CssBaseline />\n      <Router>\n        <Routes>\n          <Route path=\"/\" element={<TemplateWizard />} />\n        </Routes>\n      </Router>\n    </ThemeProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,cAAc,MAAM,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGpD,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACT,aAAa;IAACM,KAAK,EAAEA,KAAM;IAAAK,QAAA,gBAC1BF,OAAA,CAACR,WAAW;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACfN,OAAA,CAACN,MAAM;MAAAQ,QAAA,eACLF,OAAA,CAACL,MAAM;QAAAO,QAAA,eACLF,OAAA,CAACJ,KAAK;UAACW,IAAI,EAAC,GAAG;UAACC,OAAO,eAAER,OAAA,CAACF,cAAc;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEpB;AAACG,EAAA,GAXQR,GAAG;AAaZ,eAAeA,GAAG;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}