[{"C:\\Spaces\\WebsiteRosetta\\project-044\\VastWizardv2.0\\frontend\\src\\index.tsx": "1", "C:\\Spaces\\WebsiteRosetta\\project-044\\VastWizardv2.0\\frontend\\src\\app.tsx": "2", "C:\\Spaces\\WebsiteRosetta\\project-044\\VastWizardv2.0\\frontend\\src\\App.tsx": "3"}, {"size": 434, "mtime": 1752004870120, "results": "4", "hashOfConfig": "5"}, {"size": 1438, "mtime": 1751878608137, "results": "6", "hashOfConfig": "5"}, {"size": 601, "mtime": 1752004586132, "results": "7", "hashOfConfig": "5"}, {"filePath": "8", "messages": "9", "suppressedMessages": "10", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1r6fcqh", {"filePath": "11", "messages": "12", "suppressedMessages": "13", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "14", "messages": "15", "suppressedMessages": "16", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Spaces\\WebsiteRosetta\\project-044\\VastWizardv2.0\\frontend\\src\\index.tsx", [], [], "C:\\Spaces\\WebsiteRosetta\\project-044\\VastWizardv2.0\\frontend\\src\\app.tsx", [], [], "C:\\Spaces\\WebsiteRosetta\\project-044\\VastWizardv2.0\\frontend\\src\\App.tsx", ["17", "18", "19"], [], {"ruleId": "20", "severity": 1, "message": "21", "line": 7, "column": 10, "nodeType": "22", "messageId": "23", "endLine": 7, "endColumn": 13}, {"ruleId": "20", "severity": 1, "message": "24", "line": 7, "column": 15, "nodeType": "22", "messageId": "23", "endLine": 7, "endColumn": 24}, {"ruleId": "20", "severity": 1, "message": "25", "line": 7, "column": 26, "nodeType": "22", "messageId": "23", "endLine": 7, "endColumn": 36}, "@typescript-eslint/no-unused-vars", "'Box' is defined but never used.", "Identifier", "unusedVar", "'Container' is defined but never used.", "'Typography' is defined but never used."]