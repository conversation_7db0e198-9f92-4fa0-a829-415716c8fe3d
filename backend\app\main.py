from fastapi import Fast<PERSON><PERSON>, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
import os
import logging
from dotenv import load_dotenv

# Import OpenRouter service
from app.services.openrouter_service import OpenRouterService

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

app = FastAPI(
    title="Vast.ai Template Wizard API",
    description="API for generating Vast.ai templates based on user requirements",
    version="1.0.0"
)

# CORS middleware configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class TemplateRequest(BaseModel):
    requirements: str = Field(..., description="User's requirements in natural language")
    models: Optional[List[str]] = Field(None, description="List of ML models to include")
    frameworks: Optional[List[str]] = Field(None, description="ML frameworks to include")
    python_version: Optional[str] = Field("3.10", description="Python version to use")
    cuda_version: Optional[str] = Field("12.1", description="CUDA version to use")
    gpu_constraints: Optional[Dict[str, Any]] = Field(None, description="GPU requirements")

class TemplateResponse(BaseModel):
    template: dict
    cli_command: str
    gpu_recommendations: dict
    form_fields: dict

@app.get("/")
async def root():
    return {"message": "Vast.ai Template Wizard API is running"}

@app.post("/generate-template/", response_model=TemplateResponse)
async def generate_template(request: TemplateRequest):
    """
    Generate a Vast.ai template based on user requirements using OpenRouter's Mistral 7B model.
    """
    try:
        logger.info(f"Generating template for requirements: {request.requirements[:100]}...")
        
        # Prepare context for the AI
        context = {
            "models": request.models,
            "frameworks": request.frameworks,
            "python_version": request.python_version,
            "cuda_version": request.cuda_version,
            "gpu_constraints": request.gpu_constraints or {}
        }
        
        # Generate template using OpenRouter
        result = OpenRouterService.generate_template(
            requirements=request.requirements,
            context=context
        )
        
        # Prepare form fields for the UI
        form_fields = {
            "image_path": result['template'].get('image', ''),
            "version_tag": "latest",
            "docker_options": ' '.join([f'-p {port}' for port in result['template'].get('ports', [])]),
            "environment_vars": ' '.join([f'{k}={v}' for k, v in result['template'].get('environment', {}).items()]),
            "onstart_script": result['template'].get('onstart_script', ''),
            "extra_filters": ''
        }
        
        return {
            "template": result['template'],
            "cli_command": result['cli_command'],
            "gpu_recommendations": result['gpu_recommendations'],
            "form_fields": form_fields
        }
        
    except ValueError as e:
        logger.error(f"Validation error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error generating template: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate template: {str(e)}"
        )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app.main:app", host="0.0.0.0", port=8000, reload=True)
