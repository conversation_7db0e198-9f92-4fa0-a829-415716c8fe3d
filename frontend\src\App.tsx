import React from 'react';
import { ThemeProvider } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import theme from './theme';

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Router>
        <div className="min-h-screen bg-black text-white flex items-center justify-center">
          <h1 className="text-4xl font-bold holographic">🚀 VAST WIZARD v2.0 🚀</h1>
        </div>
      </Router>
    </ThemeProvider>
  );
}

export default App;
