{"name": "vast-wizard-v2", "version": "2.0.0", "description": "AI-Powered Template Generator for Vast.ai - The Ultimate Tool for Non-Technical Users", "main": "index.js", "scripts": {"dev": "cd frontend && npm start", "build": "cd frontend && npm run build", "install-frontend": "cd frontend && npm install", "start": "cd frontend && npm start"}, "keywords": ["vast.ai", "ai", "template", "generator", "gpu", "machine-learning", "docker", "cyberpunk", "conversational-ai"], "author": "VastWizard Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/vast-wizard-v2.git"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}